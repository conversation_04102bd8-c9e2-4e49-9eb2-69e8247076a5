#!/usr/bin/env python3
"""
Test script for Windows Application Scanner
Performs basic functionality tests
"""

import os
import sys
import tempfile
from windows_app_scanner import WindowsAppScanner
from app_filters import should_include_application, is_system_application

def test_filter_functions():
    """Test the application filtering functions"""
    print("Testing filter functions...")
    
    # Test system applications (should be excluded)
    test_cases_exclude = [
        {"name": "Calculator", "publisher": "Microsoft Corporation", "install_location": ""},
        {"name": "Microsoft Edge", "publisher": "Microsoft Corporation", "install_location": ""},
        {"name": "Windows Security", "publisher": "Microsoft Corporation", "install_location": ""},
        {"name": "Intel Graphics Driver", "publisher": "Intel Corporation", "install_location": ""},
        {"name": "Visual C++ Redistributable", "publisher": "Microsoft Corporation", "install_location": ""},
    ]
    
    # Test third-party applications (should be included)
    test_cases_include = [
        {"name": "Google Chrome", "publisher": "Google LLC", "install_location": "C:\\Program Files\\Google\\Chrome"},
        {"name": "Adobe Photoshop", "publisher": "Adobe Inc.", "install_location": "C:\\Program Files\\Adobe\\Photoshop"},
        {"name": "VLC Media Player", "publisher": "VideoLAN", "install_location": "C:\\Program Files\\VideoLAN\\VLC"},
        {"name": "7-Zip", "publisher": "Igor Pavlov", "install_location": "C:\\Program Files\\7-Zip"},
        {"name": "Microsoft Office", "publisher": "Microsoft Corporation", "install_location": "C:\\Program Files\\Microsoft Office"},
    ]
    
    print("Testing exclusions (should be False):")
    for test_case in test_cases_exclude:
        result = should_include_application(test_case)
        status = "✓ PASS" if not result else "✗ FAIL"
        print(f"  {test_case['name']}: {status}")
    
    print("\nTesting inclusions (should be True):")
    for test_case in test_cases_include:
        result = should_include_application(test_case)
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {test_case['name']}: {status}")

def test_scanner_initialization():
    """Test scanner initialization"""
    print("\nTesting scanner initialization...")
    try:
        scanner = WindowsAppScanner()
        print("✓ Scanner initialized successfully")
        return scanner
    except Exception as e:
        print(f"✗ Scanner initialization failed: {e}")
        return None

def test_registry_access():
    """Test registry access"""
    print("\nTesting registry access...")
    try:
        scanner = WindowsAppScanner()
        scanner.get_registry_applications()
        app_count = len(scanner.applications)
        print(f"✓ Registry scan completed. Found {app_count} applications")
        return app_count > 0
    except Exception as e:
        print(f"✗ Registry access failed: {e}")
        return False

def test_export_functions():
    """Test export functionality"""
    print("\nTesting export functions...")
    
    # Create a test scanner with sample data
    scanner = WindowsAppScanner()
    scanner.applications = [
        {
            'name': 'Test Application',
            'version': '1.0.0',
            'publisher': 'Test Publisher',
            'size_mb': 100.5,
            'install_location': 'C:\\Test\\App',
            'executable_path': 'C:\\Test\\App\\test.exe',
            'install_date': '20240115',
            'source': 'Test'
        }
    ]
    
    # Test exports in temporary directory
    with tempfile.TemporaryDirectory() as temp_dir:
        try:
            # Test CSV export
            csv_file = os.path.join(temp_dir, "test.csv")
            scanner.export_to_csv(csv_file)
            if os.path.exists(csv_file) and os.path.getsize(csv_file) > 0:
                print("✓ CSV export successful")
            else:
                print("✗ CSV export failed")
            
            # Test JSON export
            json_file = os.path.join(temp_dir, "test.json")
            scanner.export_to_json(json_file)
            if os.path.exists(json_file) and os.path.getsize(json_file) > 0:
                print("✓ JSON export successful")
            else:
                print("✗ JSON export failed")
            
            # Test text export
            txt_file = os.path.join(temp_dir, "test.txt")
            scanner.export_to_text(txt_file)
            if os.path.exists(txt_file) and os.path.getsize(txt_file) > 0:
                print("✓ Text export successful")
            else:
                print("✗ Text export failed")
                
        except Exception as e:
            print(f"✗ Export test failed: {e}")

def test_size_calculation():
    """Test directory size calculation"""
    print("\nTesting size calculation...")
    try:
        scanner = WindowsAppScanner()
        
        # Test with Windows directory (should return some size)
        windows_size = scanner.calculate_directory_size("C:\\Windows\\System32")
        if windows_size > 0:
            print(f"✓ Size calculation working. System32 size: {windows_size} MB")
        else:
            print("⚠ Size calculation returned 0 (may be permission issue)")
        
        # Test with non-existent directory
        fake_size = scanner.calculate_directory_size("C:\\NonExistentDirectory")
        if fake_size == 0:
            print("✓ Non-existent directory handling correct")
        else:
            print("✗ Non-existent directory handling failed")
            
    except Exception as e:
        print(f"✗ Size calculation test failed: {e}")

def run_quick_scan():
    """Run a quick scan to test overall functionality"""
    print("\nRunning quick scan test...")
    try:
        scanner = WindowsAppScanner()
        
        # Run a limited scan (registry only for speed)
        scanner.get_registry_applications()
        
        app_count = len(scanner.applications)
        print(f"✓ Quick scan completed. Found {app_count} third-party applications")
        
        if app_count > 0:
            print(f"Sample applications found:")
            for i, app in enumerate(scanner.applications[:3]):  # Show first 3
                print(f"  {i+1}. {app['name']} ({app['size_mb']} MB)")
        
        return True
        
    except Exception as e:
        print(f"✗ Quick scan failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Windows Application Scanner - Test Suite")
    print("=" * 50)
    
    # Run tests
    test_filter_functions()
    scanner = test_scanner_initialization()
    
    if scanner:
        test_registry_access()
        test_export_functions()
        test_size_calculation()
        run_quick_scan()
    
    print("\n" + "=" * 50)
    print("Test suite completed!")
    print("\nIf all tests passed, the scanner should work correctly.")
    print("If any tests failed, check the error messages above.")
    print("\nTo run the full scanner, execute: python windows_app_scanner.py")

if __name__ == "__main__":
    main()
