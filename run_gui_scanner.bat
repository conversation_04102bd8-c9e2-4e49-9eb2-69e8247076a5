@echo off
echo Windows Application Scanner - GUI Version
echo ========================================
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"
echo Current directory: %CD%
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install required packages if not already installed
echo Installing required Python packages...
pip install pywin32 wmi

if errorlevel 1 (
    echo.
    echo WARNING: Some packages may not have installed correctly.
    echo The scanner will still work but may have limited functionality.
    echo.
)

REM Check if the GUI scanner file exists
if not exist "gui_scanner.py" (
    echo ERROR: gui_scanner.py not found in current directory
    echo Please make sure all files are in the same folder
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo.
echo Starting GUI application scanner...
echo.

REM Run the GUI scanner
python gui_scanner.py

echo.
echo GUI application closed.
pause
