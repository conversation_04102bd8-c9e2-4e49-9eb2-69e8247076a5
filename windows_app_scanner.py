#!/usr/bin/env python3
"""
Windows Application Scanner
Scans and inventories installed applications on Windows systems
"""

import os
import sys
import json
import csv
import winreg
import subprocess
from datetime import datetime
from pathlib import Path
import logging

try:
    import wmi
    WMI_AVAILABLE = True
except ImportError:
    WMI_AVAILABLE = False
    print("Warning: WMI module not available. Install with: pip install wmi")

from app_filters import should_include_application

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WindowsAppScanner:
    def __init__(self):
        self.applications = []
        self.wmi_connection = None
        if WMI_AVAILABLE:
            try:
                self.wmi_connection = wmi.WMI()
            except Exception as e:
                logger.warning(f"Could not initialize WMI connection: {e}")
    
    def get_registry_applications(self):
        """Scan Windows Registry for installed applications"""
        logger.info("Scanning Windows Registry for installed applications...")
        
        # Registry keys to scan (both 32-bit and 64-bit)
        registry_keys = [
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_LOCAL_MACHINE, r"SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall"),
            (winreg.HKEY_CURRENT_USER, r"SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall"),
        ]
        
        for hkey, subkey_path in registry_keys:
            try:
                with winreg.OpenKey(hkey, subkey_path) as key:
                    self._scan_registry_key(key)
            except FileNotFoundError:
                logger.debug(f"Registry key not found: {subkey_path}")
            except PermissionError:
                logger.warning(f"Permission denied accessing registry key: {subkey_path}")
            except Exception as e:
                logger.error(f"Error accessing registry key {subkey_path}: {e}")
    
    def _scan_registry_key(self, key):
        """Scan a specific registry key for application entries"""
        try:
            i = 0
            while True:
                try:
                    subkey_name = winreg.EnumKey(key, i)
                    with winreg.OpenKey(key, subkey_name) as subkey:
                        app_info = self._extract_app_info_from_registry(subkey)
                        if app_info and should_include_application(app_info):
                            # Check if we already have this app (avoid duplicates)
                            if not any(app['name'] == app_info['name'] and 
                                     app['publisher'] == app_info['publisher'] 
                                     for app in self.applications):
                                self.applications.append(app_info)
                    i += 1
                except OSError:
                    break
        except Exception as e:
            logger.error(f"Error scanning registry key: {e}")
    
    def _extract_app_info_from_registry(self, subkey):
        """Extract application information from a registry subkey"""
        try:
            app_info = {
                'name': '',
                'version': '',
                'publisher': '',
                'install_location': '',
                'install_date': '',
                'size_mb': 0,
                'executable_path': '',
                'uninstall_string': '',
                'source': 'Registry'
            }
            
            # Map registry values to our app_info structure
            value_mappings = {
                'DisplayName': 'name',
                'DisplayVersion': 'version',
                'Publisher': 'publisher',
                'InstallLocation': 'install_location',
                'InstallDate': 'install_date',
                'EstimatedSize': 'size_kb',
                'UninstallString': 'uninstall_string',
            }
            
            for reg_value, info_key in value_mappings.items():
                try:
                    value, _ = winreg.QueryValueEx(subkey, reg_value)
                    if reg_value == 'EstimatedSize':
                        # Convert KB to MB
                        app_info['size_mb'] = round(int(value) / 1024, 2) if value else 0
                    else:
                        app_info[info_key] = str(value) if value else ''
                except FileNotFoundError:
                    pass  # Value doesn't exist, keep default
            
            # Skip if no display name
            if not app_info['name']:
                return None
            
            # Try to find executable path
            if app_info['install_location']:
                app_info['executable_path'] = self._find_main_executable(app_info['install_location'])
            
            return app_info
            
        except Exception as e:
            logger.debug(f"Error extracting app info from registry: {e}")
            return None
    
    def get_wmi_applications(self):
        """Get applications using WMI (Windows Management Instrumentation)"""
        if not self.wmi_connection:
            logger.warning("WMI connection not available, skipping WMI scan")
            return
        
        logger.info("Scanning applications using WMI...")
        try:
            for product in self.wmi_connection.Win32_Product():
                app_info = {
                    'name': product.Name or '',
                    'version': product.Version or '',
                    'publisher': product.Vendor or '',
                    'install_location': product.InstallLocation or '',
                    'install_date': product.InstallDate or '',
                    'size_mb': 0,  # WMI doesn't provide size info reliably
                    'executable_path': '',
                    'uninstall_string': '',
                    'source': 'WMI'
                }
                
                if app_info['name'] and should_include_application(app_info):
                    # Check for duplicates
                    if not any(app['name'] == app_info['name'] and 
                             app['publisher'] == app_info['publisher'] 
                             for app in self.applications):
                        self.applications.append(app_info)
                        
        except Exception as e:
            logger.error(f"Error scanning applications with WMI: {e}")
    
    def _find_main_executable(self, install_path):
        """Try to find the main executable file in the installation directory"""
        if not install_path or not os.path.exists(install_path):
            return ''
        
        try:
            # Look for common executable patterns
            for root, dirs, files in os.walk(install_path):
                for file in files:
                    if file.lower().endswith('.exe'):
                        # Prioritize files that might be the main executable
                        file_lower = file.lower()
                        if any(pattern in file_lower for pattern in ['setup', 'install', 'uninstall']):
                            continue  # Skip installer/uninstaller files
                        
                        return os.path.join(root, file)
                
                # Don't go too deep to avoid performance issues
                if len(root.split(os.sep)) - len(install_path.split(os.sep)) > 2:
                    break
                    
        except Exception as e:
            logger.debug(f"Error finding executable in {install_path}: {e}")
        
        return ''
    
    def calculate_directory_size(self, path):
        """Calculate the total size of a directory in MB"""
        if not path or not os.path.exists(path):
            return 0
        
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(path):
                for filename in filenames:
                    filepath = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(filepath)
                    except (OSError, FileNotFoundError):
                        pass  # Skip files we can't access
            
            return round(total_size / (1024 * 1024), 2)  # Convert to MB
            
        except Exception as e:
            logger.debug(f"Error calculating directory size for {path}: {e}")
            return 0
    
    def enhance_size_information(self):
        """Enhance size information for applications where it's missing"""
        logger.info("Enhancing size information for applications...")
        
        for app in self.applications:
            if app['size_mb'] == 0 and app['install_location']:
                logger.debug(f"Calculating size for {app['name']}...")
                app['size_mb'] = self.calculate_directory_size(app['install_location'])
    
    def scan_all_applications(self):
        """Perform complete application scan using all available methods"""
        logger.info("Starting comprehensive application scan...")
        
        # Clear previous results
        self.applications = []
        
        # Scan using different methods
        self.get_registry_applications()
        self.get_wmi_applications()
        
        # Enhance the collected data
        self.enhance_size_information()
        
        # Sort by size (largest first)
        self.applications.sort(key=lambda x: x['size_mb'], reverse=True)
        
        logger.info(f"Scan complete. Found {len(self.applications)} third-party applications.")
        return self.applications

    def export_to_csv(self, filename='installed_applications.csv'):
        """Export application list to CSV format"""
        logger.info(f"Exporting to CSV: {filename}")

        fieldnames = ['name', 'version', 'publisher', 'size_mb', 'install_location',
                     'executable_path', 'install_date', 'source']

        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()

            for app in self.applications:
                # Create a clean row for CSV export
                row = {field: app.get(field, '') for field in fieldnames}
                writer.writerow(row)

        logger.info(f"CSV export complete: {filename}")

    def export_to_json(self, filename='installed_applications.json'):
        """Export application list to JSON format"""
        logger.info(f"Exporting to JSON: {filename}")

        export_data = {
            'scan_date': datetime.now().isoformat(),
            'total_applications': len(self.applications),
            'applications': self.applications
        }

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(export_data, jsonfile, indent=2, ensure_ascii=False)

        logger.info(f"JSON export complete: {filename}")

    def export_to_text(self, filename='installed_applications.txt'):
        """Export application list to readable text format"""
        logger.info(f"Exporting to text: {filename}")

        with open(filename, 'w', encoding='utf-8') as txtfile:
            txtfile.write("WINDOWS INSTALLED APPLICATIONS INVENTORY\n")
            txtfile.write("=" * 50 + "\n\n")
            txtfile.write(f"Scan Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            txtfile.write(f"Total Applications Found: {len(self.applications)}\n\n")
            txtfile.write("Applications sorted by size (largest first):\n")
            txtfile.write("-" * 50 + "\n\n")

            for i, app in enumerate(self.applications, 1):
                txtfile.write(f"{i:3d}. {app['name']}\n")
                txtfile.write(f"     Version: {app['version']}\n")
                txtfile.write(f"     Publisher: {app['publisher']}\n")
                txtfile.write(f"     Size: {app['size_mb']} MB\n")
                if app['install_location']:
                    txtfile.write(f"     Install Path: {app['install_location']}\n")
                if app['executable_path']:
                    txtfile.write(f"     Executable: {app['executable_path']}\n")
                if app['install_date']:
                    txtfile.write(f"     Install Date: {app['install_date']}\n")
                txtfile.write(f"     Source: {app['source']}\n")
                txtfile.write("\n")

        logger.info(f"Text export complete: {filename}")

    def print_summary(self):
        """Print a summary of the scan results"""
        if not self.applications:
            print("No applications found.")
            return

        print(f"\n{'='*60}")
        print(f"SCAN SUMMARY")
        print(f"{'='*60}")
        print(f"Total third-party applications found: {len(self.applications)}")

        total_size = sum(app['size_mb'] for app in self.applications)
        print(f"Total estimated size: {total_size:.2f} MB ({total_size/1024:.2f} GB)")

        print(f"\nTop 10 largest applications:")
        print(f"{'-'*60}")

        for i, app in enumerate(self.applications[:10], 1):
            size_str = f"{app['size_mb']:.1f} MB" if app['size_mb'] > 0 else "Unknown size"
            print(f"{i:2d}. {app['name'][:40]:<40} {size_str:>15}")

        print(f"\nPublisher distribution:")
        print(f"{'-'*30}")

        publishers = {}
        for app in self.applications:
            pub = app['publisher'] or 'Unknown'
            publishers[pub] = publishers.get(pub, 0) + 1

        # Show top 5 publishers
        top_publishers = sorted(publishers.items(), key=lambda x: x[1], reverse=True)[:5]
        for pub, count in top_publishers:
            print(f"{pub[:25]:<25} {count:>3} apps")


def main():
    """Main function to run the application scanner"""
    print("Windows Application Scanner")
    print("=" * 30)
    print("Scanning for installed third-party applications...")
    print("This may take a few minutes...\n")

    try:
        scanner = WindowsAppScanner()
        applications = scanner.scan_all_applications()

        if not applications:
            print("No third-party applications found.")
            return

        # Print summary
        scanner.print_summary()

        # Export to all formats
        print(f"\n{'='*60}")
        print("EXPORTING RESULTS")
        print(f"{'='*60}")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        csv_filename = f"installed_apps_{timestamp}.csv"
        json_filename = f"installed_apps_{timestamp}.json"
        txt_filename = f"installed_apps_{timestamp}.txt"

        scanner.export_to_csv(csv_filename)
        scanner.export_to_json(json_filename)
        scanner.export_to_text(txt_filename)

        print(f"\nExport complete! Files created:")
        print(f"  - {csv_filename} (CSV format)")
        print(f"  - {json_filename} (JSON format)")
        print(f"  - {txt_filename} (Human-readable text)")

        print(f"\nThese files contain a prioritized list of applications")
        print(f"sorted by size to help with new computer setup.")

    except KeyboardInterrupt:
        print("\nScan interrupted by user.")
    except Exception as e:
        logger.error(f"An error occurred during scanning: {e}")
        print(f"Error: {e}")
        print("Please run as administrator if you encounter permission issues.")


if __name__ == "__main__":
    main()
