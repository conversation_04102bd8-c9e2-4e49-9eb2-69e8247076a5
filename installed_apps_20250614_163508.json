{"scan_date": "2025-06-14T16:35:08.669067", "total_applications": 142, "applications": [{"name": "IntelliJ IDEA 2025.1.1.1", "version": "251.25410.129", "publisher": "JetBrains s.r.o.", "install_location": "F:\\Program Files\\JetBrains\\IntelliJ IDEA 2025.1.1.1", "install_date": "", "size_mb": 4375.15, "executable_path": "F:\\Program Files\\JetBrains\\IntelliJ IDEA 2025.1.1.1\\bin\\elevator.exe", "uninstall_string": "F:\\Program Files\\JetBrains\\IntelliJ IDEA 2025.1.1.1\\bin\\Uninstall.exe", "source": "Registry"}, {"name": "Autodesk AutoCAD 2022 - 简体中文 (Simplified Chinese)", "version": "*********", "publisher": "Autodesk, Inc.", "install_location": "F:\\Program Files (x86)", "install_date": "20230621", "size_mb": 4096.41, "executable_path": "F:\\Program Files (x86)\\alipay\\aliedit\\*********\\HotFixTool.exe", "uninstall_string": "D:\\Program Files\\Autodesk\\AdODIS\\V1\\Installer.exe -i uninstall --trigger_point system -m C:\\ProgramData\\Autodesk\\ODIS\\metadata\\{7EE008AE-5D6A-3AA3-ACDF-EC695BCCA205}\\bundleManifest.xml -x C:\\ProgramData\\Autodesk\\ODIS\\metadata\\{7EE008AE-5D6A-3AA3-ACDF-EC695BCCA205}\\SetupRes\\manifest.xsd", "source": "Registry"}, {"name": "SOLIDWORKS 2017 Chinese Simplified Resources", "version": "25.100.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS\\", "install_date": "20201206", "size_mb": 3411.87, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "AutoCAD 2022", "version": "*********", "publisher": "Autodesk", "install_location": "F:\\Program Files (x86)\\AutoCAD 2022\\", "install_date": "20230621", "size_mb": 2521.93, "executable_path": "F:\\Program Files (x86)\\AutoCAD 2022\\acad.exe", "uninstall_string": "", "source": "Registry"}, {"name": "PyCharm Community Edition 2023.1", "version": "231.8109.197", "publisher": "JetBrains s.r.o.", "install_location": "F:\\Program Files\\JetBrains\\PyCharm Community Edition 2023.1", "install_date": "", "size_mb": 1651.92, "executable_path": "F:\\Program Files\\JetBrains\\PyCharm Community Edition 2023.1\\bin\\elevator.exe", "uninstall_string": "F:\\Program Files\\JetBrains\\PyCharm Community Edition 2023.1\\bin\\Uninstall.exe", "source": "Registry"}, {"name": "ABBYY FineReader PDF", "version": "16.0.7300", "publisher": "ABBYY Development, Inc.", "install_location": "F:\\Program Files\\ABBYY FineReader 16\\", "install_date": "20240505", "size_mb": 1295.36, "executable_path": "F:\\Program Files\\ABBYY FineReader 16\\AbbyySTI.exe", "uninstall_string": "MsiExec.exe /I{F16000FE-0003-6400-0000-074957833700}", "source": "Registry"}, {"name": "福昕高级PDF编辑器", "version": "9.3.0.10826", "publisher": "福建福昕软件开发股份有限公司", "install_location": "D:\\Program Files (x86)\\Foxit Software\\Foxit PhantomPDF\\", "install_date": "20190520", "size_mb": 1252.49, "executable_path": "D:\\Program Files (x86)\\Foxit Software\\Foxit PhantomPDF\\64BitMailAgent.exe", "uninstall_string": "MsiExec.exe /I{DB5A079E-C1DA-11E8-BD2C-000C296BF2A5}", "source": "Registry"}, {"name": "ima.copilot", "version": "135.0.7049.2674", "publisher": "The ima.copilot Authors", "install_location": "F:\\Users\\ima.copilotApplication\\ima.copilot", "install_date": "20250512", "size_mb": 1040.88, "executable_path": "F:\\Users\\ima.copilotApplication\\ima.copilot\\chrome_proxy.exe", "uninstall_string": "\"F:\\Users\\ima.copilotApplication\\ima.copilot\\135.0.7049.2674\\Installer\\setup.exe\" --uninstall", "source": "Registry"}, {"name": "reaConverter Pro 7.680", "version": "7.680", "publisher": "LRepacks", "install_location": "F:\\Program Files (x86)\\reaConverter Pro\\", "install_date": "20231221", "size_mb": 866.0, "executable_path": "F:\\Program Files (x86)\\reaConverter Pro\\cons_rcp.exe", "uninstall_string": "\"F:\\Program Files (x86)\\reaConverter Pro\\unins000.exe\"", "source": "Registry"}, {"name": "Google Chrome", "version": "137.0.7151.104", "publisher": "Google LLC", "install_location": "F:\\Program Files\\Google\\Chrome\\Application", "install_date": "20250613", "size_mb": 802.72, "executable_path": "F:\\Program Files\\Google\\Chrome\\Application\\chrome.exe", "uninstall_string": "\"F:\\Program Files\\Google\\Chrome\\Application\\137.0.7151.104\\Installer\\setup.exe\" --uninstall --channel=stable --system-level --verbose-logging", "source": "Registry"}, {"name": "VMware Workstation", "version": "15.0.2", "publisher": "VMware, Inc.", "install_location": "", "install_date": "20250412", "size_mb": 781.53, "executable_path": "", "uninstall_string": "", "source": "Registry"}, {"name": "MinerU", "version": "0.4.1", "publisher": "opendatalab.com", "install_location": "", "install_date": "", "size_mb": 747.57, "executable_path": "", "uninstall_string": "\"F:\\Users\\Administrator\\AppData\\Local\\Programs\\MinerU\\Uninstall MinerU.exe\" /currentuser", "source": "Registry"}, {"name": "Termius 9.20.0", "version": "9.20.0", "publisher": "Termius Corporation", "install_location": "", "install_date": "", "size_mb": 703.34, "executable_path": "", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Termius\\Uninstall Termius.exe\" /currentuser", "source": "Registry"}, {"name": "腾讯会议", "version": "3.30.30.420", "publisher": "腾讯科技(深圳)有限公司", "install_location": "\"F:\\Program Files\\Tencent\\WeMeet\\3.30.30.420\"", "install_date": "", "size_mb": 611.98, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\Tencent\\WeMeet\\3.30.30.420\\WeMeetUninstall.exe\"", "source": "Registry"}, {"name": "<PERSON><PERSON> (User)", "version": "1.98.2", "publisher": "SPRING (SG) PTE. LTD", "install_location": "F:\\Users\\Administrator\\AppData\\Local\\Programs\\Trae\\", "install_date": "20250514", "size_mb": 597.22, "executable_path": "F:\\Users\\Administrator\\AppData\\Local\\Programs\\Trae\\Trae.exe", "uninstall_string": "\"F:\\Users\\Administrator\\AppData\\Local\\Programs\\Trae\\unins000.exe\"", "source": "Registry"}, {"name": "AdsPower Global 7.3.26", "version": "7.3.26", "publisher": "AdsPower", "install_location": "", "install_date": "", "size_mb": 523.13, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\AdsPower Global\\Uninstall AdsPower Global.exe\" /allusers", "source": "Registry"}, {"name": "印象笔记 v. 7.2.44", "version": "7.2.44.8525", "publisher": "Beijing Yinxiang Biji Technologies Co., Ltd.", "install_location": "F:\\Program Files (x86)\\Yinxiang Biji\\印象笔记\\", "install_date": "20240626", "size_mb": 493.05, "executable_path": "F:\\Program Files (x86)\\Yinxiang Biji\\印象笔记\\ENScript.exe", "uninstall_string": "MsiExec.exe /X{C911BEFC-2ED6-11EF-8FAB-A02919234D82}", "source": "Registry"}, {"name": "<PERSON><PERSON><PERSON> (User)", "version": "1.1.2", "publisher": "Anysphere", "install_location": "F:\\Users\\Administrator\\AppData\\Local\\Programs\\cursor\\", "install_date": "20250613", "size_mb": 460.55, "executable_path": "F:\\Users\\Administrator\\AppData\\Local\\Programs\\cursor\\Cursor.exe", "uninstall_string": "\"F:\\Users\\Administrator\\AppData\\Local\\Programs\\cursor\\unins000.exe\"", "source": "Registry"}, {"name": "calibre 64bit", "version": "7.10.0", "publisher": "<PERSON><PERSON>", "install_location": "D:\\Program Files\\Calibre2\\", "install_date": "20240504", "size_mb": 460.45, "executable_path": "D:\\Program Files\\Calibre2\\calibre-complete.exe", "uninstall_string": "MsiExec.exe /I{CBBB212A-8EDB-4E87-8584-D87FB2037167}", "source": "Registry"}, {"name": "阿里云盘", "version": "6.8.7", "publisher": "Alibaba Group", "install_location": "D:\\Program Files (x86)\\aDrive", "install_date": "", "size_mb": 440.53, "executable_path": "D:\\Program Files (x86)\\aDrive\\aDrive.exe", "uninstall_string": "\"D:\\Program Files (x86)\\aDrive\\Uninstall.exe\"", "source": "Registry"}, {"name": "AnyTXT Searcher 1.3", "version": "", "publisher": "CBEWIN", "install_location": "D:\\Program Files (x86)\\AnyTXT Searcher\\", "install_date": "20230719", "size_mb": 388.4, "executable_path": "D:\\Program Files (x86)\\AnyTXT Searcher\\ATDHelper.exe", "uninstall_string": "\"D:\\Program Files (x86)\\AnyTXT Searcher\\unins000.exe\"", "source": "Registry"}, {"name": "draw.io 27.0.9", "version": "27.0.9", "publisher": "JGraph", "install_location": "", "install_date": "", "size_mb": 376.17, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\draw.io\\Uninstall draw.io.exe\" /allusers", "source": "Registry"}, {"name": "Cherry Studio", "version": "1.4.1", "publisher": "<EMAIL>", "install_location": "", "install_date": "", "size_mb": 367.15, "executable_path": "", "uninstall_string": "\"F:\\Users\\Administrator\\AppData\\Local\\Programs\\Cherry Studio\\Uninstall Cherry Studio.exe\" /currentuser", "source": "Registry"}, {"name": "CAXA CAD 电子图板 2020 (x64)", "version": "20.0.0.6460", "publisher": "北京数码大方科技股份有限公司", "install_location": "D:\\Program Files\\CAXA\\CAXA CAD\\2020", "install_date": "20211124", "size_mb": 353.45, "executable_path": "D:\\Program Files\\CAXA\\CAXA CAD\\2020\\uninst.exe", "uninstall_string": "D:\\Program Files\\CAXA\\CAXA CAD\\2020\\uninst.exe", "source": "Registry"}, {"name": "Git", "version": "2.48.1", "publisher": "The Git Development Community", "install_location": "F:\\Program Files\\Git\\", "install_date": "20250216", "size_mb": 347.73, "executable_path": "F:\\Program Files\\Git\\git-bash.exe", "uninstall_string": "\"F:\\Program Files\\Git\\unins000.exe\"", "source": "Registry"}, {"name": "微信输入法", "version": "1.3.2.13", "publisher": "腾讯科技(深圳)有限公司", "install_location": "F:\\Program Files\\Tencent\\WeType", "install_date": "", "size_mb": 322.16, "executable_path": "F:\\Program Files\\Tencent\\WeType\\1.3.2.13\\CrashSender1500.exe", "uninstall_string": "\"F:\\Program Files\\Tencent\\WeType\\1.3.2.13\\Uninstall.exe\"", "source": "Registry"}, {"name": "Java SE Development Kit 8 Update 31 (64-bit)", "version": "8.0.310.13", "publisher": "Oracle Corporation", "install_location": "F:\\Program Files\\Java\\jdk1.8.0_31\\", "install_date": "20221116", "size_mb": 312.05, "executable_path": "", "uninstall_string": "MsiExec.exe /I{64A3A4F4-B792-11D6-A78A-00B0D0180310}", "source": "Registry"}, {"name": "Notion 4.13.0", "version": "4.13.0", "publisher": "Notion Labs, Inc", "install_location": "", "install_date": "", "size_mb": 292.05, "executable_path": "", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Notion\\Uninstall Notion.exe\" /currentuser", "source": "Registry"}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "version": "7.3.1", "publisher": "TTKN", "install_location": "", "install_date": "20221024", "size_mb": 277.33, "executable_path": "", "uninstall_string": "MsiExec.exe /I{38CE8FAD-2E31-4CA8-B671-1BA7A8A54B28}", "source": "Registry"}, {"name": "bilidown 1.2.4", "version": "1.2.4", "publisher": "王子周棋洛", "install_location": "", "install_date": "", "size_mb": 257.1, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\bilidown\\Uninstall bilidown.exe\" /allusers", "source": "Registry"}, {"name": "AMD Settings", "version": "2018.1206.1949.35667", "publisher": "##COMPANY_NAME##", "install_location": "C:\\Program Files\\AMD\\", "install_date": "20190518", "size_mb": 225.21, "executable_path": "C:\\Program Files\\AMD\\CIM\\BIN64\\AMDCleanupUtility.exe", "uninstall_string": "", "source": "Registry"}, {"name": "TIM", "version": "2.3.2.21158", "publisher": "腾讯科技(深圳)有限公司", "install_location": "D:\\Program Files (x86)\\Tencent\\TIM", "install_date": "", "size_mb": 217.78, "executable_path": "D:\\Program Files (x86)\\Tencent\\TIM\\TIMUninst.exe", "uninstall_string": "D:\\Program Files (x86)\\Tencent\\TIM\\TIMUninst.exe", "source": "Registry"}, {"name": "Pandoc 3.1.12.2", "version": "3.1.12.2", "publisher": "<PERSON>", "install_location": "D:\\Program Files\\Pandoc\\", "install_date": "20240308", "size_mb": 209.28, "executable_path": "D:\\Program Files\\Pandoc\\pandoc.exe", "uninstall_string": "MsiExec.exe /X{4C24B759-FAA3-492B-AD34-04DDD66D505E}", "source": "Registry"}, {"name": "All-in-One Inkjet Printer 基本设备软件", "version": "43.4.48.20110", "publisher": "Hannto Technology Co., Ltd.", "install_location": "", "install_date": "20210616", "size_mb": 205.92, "executable_path": "", "uninstall_string": "MsiExec.exe /I{3C5E1AC7-36A5-4D56-886E-A2553D9089C3}", "source": "Registry"}, {"name": "小米云服务", "version": "1.3.3", "publisher": "小米科技有限责任公司", "install_location": "", "install_date": "", "size_mb": 203.04, "executable_path": "", "uninstall_string": "\"C:\\Program Files\\MI\\Xiaomi Cloud\\Uninstall 小米云服务.exe\" /allusers", "source": "Registry"}, {"name": "Autodesk Single Sign On Component", "version": "12.3.3.1803", "publisher": "Autodesk", "install_location": "", "install_date": "20230621", "size_mb": 202.68, "executable_path": "", "uninstall_string": "MsiExec.exe /X{B9F5BDED-021C-4926-8518-4FA7114B7040}", "source": "Registry"}, {"name": "Java 8 Update 381 (64-bit)", "version": "8.0.3810.9", "publisher": "Oracle Corporation", "install_location": "D:\\Program Files\\Java\\jre-1.8\\", "install_date": "20230722", "size_mb": 197.59, "executable_path": "D:\\Program Files\\Java\\jre-1.8\\bin\\jabswitch.exe", "uninstall_string": "MsiExec.exe /I{77924AE4-039E-4CA4-87B4-2F64180381F0}", "source": "Registry"}, {"name": "Telegram Desktop", "version": "5.13.1", "publisher": "Telegram FZ-LLC", "install_location": "F:\\Users\\Administrator\\AppData\\Roaming\\Telegram Desktop\\", "install_date": "20250410", "size_mb": 173.38, "executable_path": "F:\\Users\\Administrator\\AppData\\Roaming\\Telegram Desktop\\Telegram.exe", "uninstall_string": "\"F:\\Users\\Administrator\\AppData\\Roaming\\Telegram Desktop\\unins000.exe\"", "source": "Registry"}, {"name": "VLC media player", "version": "3.0.8", "publisher": "VideoLAN", "install_location": "D:\\Program Files\\VideoLAN\\VLC", "install_date": "", "size_mb": 168.68, "executable_path": "D:\\Program Files\\VideoLAN\\VLC\\vlc-cache-gen.exe", "uninstall_string": "\"D:\\Program Files\\VideoLAN\\VLC\\uninstall.exe\"", "source": "Registry"}, {"name": "Autodesk Genuine Service", "version": "4.1.2.25", "publisher": "Autodesk", "install_location": "C:\\ProgramData\\Autodesk\\Genuine Service\\", "install_date": "20230621", "size_mb": 164.47, "executable_path": "C:\\ProgramData\\Autodesk\\Genuine Service\\message_router.exe", "uninstall_string": "MsiExec.exe /X{879EB006-4A55-4873-8BC5-2183B2B5E0F5}", "source": "Registry"}, {"name": "GitHub Desktop", "version": "3.4.20", "publisher": "GitHub, Inc.", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop", "install_date": "20250601", "size_mb": 164.01, "executable_path": "C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\GitHubDesktop.exe", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\GitHubDesktop\\Update.exe\" --uninstall", "source": "Registry"}, {"name": "SOLIDWORKS eDrawings 2017 SP0", "version": "17.0.5048", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\eDrawings\\", "install_date": "20201206", "size_mb": 152.96, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "DATAKIT CrossManager 2021 64 bit", "version": "20.4", "publisher": "DATAKIT", "install_location": "", "install_date": "20220322", "size_mb": 149.54, "executable_path": "", "uninstall_string": "MsiExec.exe /I{F2E95E1F-CC76-44FB-A9E6-F59D61D64BAB}", "source": "Registry"}, {"name": "Python 3.12.1 (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "", "size_mb": 146.48, "executable_path": "", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\Package Cache\\{86e52725-ef45-452f-ac4c-b8958718bfea}\\python-3.12.1-amd64.exe\"  /uninstall", "source": "Registry"}, {"name": "AutoCAD 2022 Language Pack - Simplified Chinese", "version": "*********", "publisher": "Autodesk", "install_location": "F:\\Program Files (x86)\\AutoCAD 2022\\", "install_date": "20230621", "size_mb": 134.2, "executable_path": "F:\\Program Files (x86)\\AutoCAD 2022\\acad.exe", "uninstall_string": "", "source": "Registry"}, {"name": "<PERSON>i", "version": "0.4.0", "publisher": "lencx", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\noi", "install_date": "20240327", "size_mb": 118.01, "executable_path": "C:\\Users\\<USER>\\AppData\\Local\\noi\\Noi.exe", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\noi\\Update.exe\" --uninstall", "source": "Registry"}, {"name": "Clash Verge", "version": "2.2.3", "publisher": "Clash Verge Rev", "install_location": "\"F:\\Program Files\\Clash Verge\"", "install_date": "", "size_mb": 112.13, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\Clash Verge\\uninstall.exe\"", "source": "Registry"}, {"name": "<PERSON>olo", "version": "0.4.7", "publisher": "Folo Team", "install_location": "C:\\Users\\<USER>\\AppData\\Local\\Folo", "install_date": "20250521", "size_mb": 109.44, "executable_path": "C:\\Users\\<USER>\\AppData\\Local\\Folo\\Folo.exe", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\Folo\\Update.exe\" --uninstall", "source": "Registry"}, {"name": "WinSCP 5.19.6", "version": "5.19.6", "publisher": "<PERSON>", "install_location": "E:\\Program Files (x86)\\WinSCP\\", "install_date": "20220422", "size_mb": 98.43, "executable_path": "E:\\Program Files (x86)\\WinSCP\\unins000.exe", "uninstall_string": "\"E:\\Program Files (x86)\\WinSCP\\unins000.exe\"", "source": "Registry"}, {"name": "Node.js", "version": "22.12.0", "publisher": "Node.js Foundation", "install_location": "", "install_date": "20241214", "size_mb": 96.73, "executable_path": "", "uninstall_string": "MsiExec.exe /I{780AD60E-7FB7-4A4D-9EEC-9C3E72148B95}", "source": "Registry"}, {"name": "Autodesk Identity Manager", "version": "1.9.18.0", "publisher": "Autodesk", "install_location": "D:\\Program Files\\Autodesk\\AdskIdentityManager\\1.9.18.0", "install_date": "20230621", "size_mb": 90.55, "executable_path": "D:\\Program Files\\Autodesk\\AdskIdentityManager\\1.9.18.0\\ADPClientService.exe", "uninstall_string": "\"D:\\Program Files\\Autodesk\\AdskIdentityManager\\uninstall.exe\"", "source": "Registry"}, {"name": "Audacity 3.7.3", "version": "3.7.3", "publisher": "Audacity Team", "install_location": "F:\\Program Files\\Audacity\\", "install_date": "20250524", "size_mb": 86.16, "executable_path": "F:\\Program Files\\Audacity\\Audacity.exe", "uninstall_string": "\"F:\\Program Files\\Audacity\\unins000.exe\"", "source": "Registry"}, {"name": "百度网盘", "version": "7.36.0", "publisher": "百度在线网络技术（北京）有限公司", "install_location": "\"D:\\Users\\Administrator\\AppData\\Roaming\\baidu\\BaiduNetdisk\"", "install_date": "", "size_mb": 82.07, "executable_path": "", "uninstall_string": "\"D:\\Users\\Administrator\\AppData\\Roaming\\baidu\\BaiduNetdisk\\uninst.exe\"", "source": "Registry"}, {"name": "AMD Software", "version": "18.12.3", "publisher": "Advanced Micro Devices, Inc.", "install_location": "C:\\Program Files\\AMD\\CIM\\BIN64", "install_date": "", "size_mb": 77.57, "executable_path": "C:\\Program Files\\AMD\\CIM\\BIN64\\AMDCleanupUtility.exe", "uninstall_string": "\"C:\\Program Files\\AMD\\CIM\\BIN64\\RadeonInstaller.exe\" /EXPRESS_UNINSTALL /IGNORE_UPGRADE /ON_REBOOT_MESSAGE:NO", "source": "Registry"}, {"name": "Autodesk Material Library Base Resolution Image Library 2022", "version": "20.3.7.0", "publisher": "Autodesk", "install_location": "", "install_date": "20230621", "size_mb": 71.51, "executable_path": "", "uninstall_string": "MsiExec.exe /X{6256584F-B04B-41D4-8A59-44E70940C473}", "source": "Registry"}, {"name": "Revo Uninstaller Pro 5.3.2", "version": "5.3.2", "publisher": "VS Revo Group, Ltd.", "install_location": "F:\\Program Files\\VS Revo Group\\Revo Uninstaller Pro\\", "install_date": "20241026", "size_mb": 71.08, "executable_path": "F:\\Program Files\\VS Revo Group\\Revo Uninstaller Pro\\RevoAppBar.exe", "uninstall_string": "\"F:\\Program Files\\VS Revo Group\\Revo Uninstaller Pro\\unins000.exe\"", "source": "Registry"}, {"name": "Sublime Text", "version": "", "publisher": "Sublime HQ Pty Ltd", "install_location": "D:\\Program Files\\Sublime Text\\", "install_date": "20230201", "size_mb": 69.14, "executable_path": "D:\\Program Files\\Sublime Text\\crash_reporter.exe", "uninstall_string": "\"D:\\Program Files\\Sublime Text\\unins000.exe\"", "source": "Registry"}, {"name": "AutoCAD Open in Desktop", "version": "1.0.20.0", "publisher": "Autodesk", "install_location": "", "install_date": "20230621", "size_mb": 57.05, "executable_path": "", "uninstall_string": "MsiExec.exe /I{1C66A0B0-784E-4777-97B3-93F843D1C8CF}", "source": "Registry"}, {"name": "AMD Settings", "version": "2018.1206.1949.35667", "publisher": "Advanced Micro Devices, Inc.", "install_location": "C:\\Program Files (x86)\\AMD\\", "install_date": "20190518", "size_mb": 52.87, "executable_path": "C:\\Program Files (x86)\\AMD\\CNext\\CCCSlim\\CCC.exe", "uninstall_string": "", "source": "Registry"}, {"name": "Formulator 3.8 ActiveX Control (Developer Edition)", "version": "", "publisher": "Hermitech Laboratory", "install_location": "D:\\Program Files (x86)\\Hermitech Laboratory\\", "install_date": "20210123", "size_mb": 51.86, "executable_path": "D:\\Program Files (x86)\\Hermitech Laboratory\\Formulator3.8\\ActiveX Control Developer\\unins000.exe", "uninstall_string": "\"D:\\Program Files (x86)\\Hermitech Laboratory\\Formulator3.8\\ActiveX Control Developer\\unins000.exe\"", "source": "Registry"}, {"name": "Python 3.12.1 Documentation (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 49.67, "executable_path": "", "uninstall_string": "MsiExec.exe /I{62667662-A580-409C-8044-55B06F774AE2}", "source": "Registry"}, {"name": "FileZilla 3.67.1", "version": "3.67.1", "publisher": "<PERSON>", "install_location": "F:\\Program Files\\FileZilla FTP Client", "install_date": "", "size_mb": 43.07, "executable_path": "F:\\Program Files\\FileZilla FTP Client\\filezilla.exe", "uninstall_string": "\"F:\\Program Files\\FileZilla FTP Client\\uninstall.exe\"", "source": "Registry"}, {"name": "ACA & MEP 2022 Object Enabler", "version": "8.4.45.0", "publisher": "Autodesk", "install_location": "F:\\Program Files (x86)\\AutoCAD 2022\\", "install_date": "20230621", "size_mb": 42.59, "executable_path": "F:\\Program Files (x86)\\AutoCAD 2022\\acad.exe", "uninstall_string": "", "source": "Registry"}, {"name": "Cisco Secure Client - AnyConnect VPN", "version": "5.1.7.80", "publisher": "Cisco Systems, Inc.", "install_location": "C:\\Program Files (x86)\\Cisco\\Cisco Secure Client\\", "install_date": "20250424", "size_mb": 40.52, "executable_path": "C:\\Program Files (x86)\\Cisco\\Cisco Secure Client\\acextwebhelper.exe", "uninstall_string": "MsiExec.exe /X{2D179838-FF98-4EAD-BEE1-4B604CFD284B}", "source": "Registry"}, {"name": "FreeMind", "version": "1.0.1", "publisher": "", "install_location": "F:\\Program Files (x86)\\FreeMind\\", "install_date": "20230707", "size_mb": 39.63, "executable_path": "F:\\Program Files (x86)\\FreeMind\\FreeMind.exe", "uninstall_string": "\"F:\\Program Files (x86)\\FreeMind\\unins000.exe\"", "source": "Registry"}, {"name": "Internet Download Manager", "version": "6.42.40", "publisher": "Tonec Inc.", "install_location": "F:\\Program Files (x86)\\Internet Download Manager\\", "install_date": "20250212", "size_mb": 30.56, "executable_path": "F:\\Program Files (x86)\\Internet Download Manager\\IDMan.exe", "uninstall_string": "F:\\Program Files (x86)\\Internet Download Manager\\Uninstall.exe", "source": "Registry"}, {"name": "Python 3.12.1 Test Suite (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 30.34, "executable_path": "", "uninstall_string": "MsiExec.exe /I{E309AE00-4FB1-4817-9172-7E198668375D}", "source": "Registry"}, {"name": "FFmpeg 5.0.0 for Audacity - x86_64", "version": "", "publisher": "", "install_location": "F:\\Program Files\\FFmpeg For Audacity\\", "install_date": "20250524", "size_mb": 27.45, "executable_path": "F:\\Program Files\\FFmpeg For Audacity\\unins000.exe", "uninstall_string": "\"F:\\Program Files\\FFmpeg For Audacity\\unins000.exe\"", "source": "Registry"}, {"name": "SOLIDWORKS Explorer 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Explorer\\", "install_date": "20201206", "size_mb": 24.34, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "Python 3.12.1 Standard Library (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 23.95, "executable_path": "", "uninstall_string": "MsiExec.exe /I{47957EE3-0E23-4075-B825-F202E913670F}", "source": "Registry"}, {"name": "Launcher Prerequisites (x64)", "version": "*******", "publisher": "Epic Games, Inc.", "install_location": "", "install_date": "", "size_mb": 20.32, "executable_path": "", "uninstall_string": "\"C:\\ProgramData\\Package Cache\\{43a03b9c-4770-409c-a999-587b60700b63}\\LauncherPrereqSetup_x64.exe\"  /uninstall", "source": "Registry"}, {"name": "WizTree v4.25", "version": "4.25", "publisher": "Antibody Software", "install_location": "F:\\Program Files\\WizTree\\", "install_date": "20250412", "size_mb": 19.64, "executable_path": "F:\\Program Files\\WizTree\\unins000.exe", "uninstall_string": "\"F:\\Program Files\\WizTree\\unins000.exe\"", "source": "Registry"}, {"name": "TAIDU钛度 Mouse Drive 版本 1.0.0.42", "version": "1.0.0.42", "publisher": "TAIDU", "install_location": "F:\\Program Files (x86)\\TAIDU钛度 Mouse Drive\\", "install_date": "20241006", "size_mb": 18.16, "executable_path": "F:\\Program Files (x86)\\TAIDU钛度 Mouse Drive\\Mouse Drive Beta.exe", "uninstall_string": "\"F:\\Program Files (x86)\\TAIDU钛度 Mouse Drive\\unins000.exe\"", "source": "Registry"}, {"name": "AutoHotkey", "version": "2.0.2", "publisher": "AutoHotkey Foundation LLC", "install_location": "D:\\Program Files\\AutoHotkey", "install_date": "", "size_mb": 17.71, "executable_path": "D:\\Program Files\\AutoHotkey\\Compiler\\Ahk2Exe.exe", "uninstall_string": "\"D:\\Program Files\\AutoHotkey\\UX\\AutoHotkeyUX.exe\" \"D:\\Program Files\\AutoHotkey\\UX\\ui-uninstall.ahk\"", "source": "Registry"}, {"name": "MotionDesign", "version": "1.4.1", "publisher": "必优科技", "install_location": "F:\\Users\\Administrator\\AppData\\Local\\BIYOO\\MotionDesign", "install_date": "", "size_mb": 17.5, "executable_path": "", "uninstall_string": "F:\\Users\\Administrator\\AppData\\Local\\BIYOO\\MotionDesign\\uninstall.exe", "source": "Registry"}, {"name": "Total Commander 64-bit (Remove or Repair)", "version": "10.00", "publisher": "Ghisler Software GmbH", "install_location": "F:\\Program Files\\totalcmd\\", "install_date": "", "size_mb": 17.23, "executable_path": "F:\\Program Files\\totalcmd\\NOCLOSE64.EXE", "uninstall_string": "F:\\Program Files\\totalcmd\\tcunin64.exe", "source": "Registry"}, {"name": "Office 16 Click-to-Run Extensibility Component", "version": "16.0.18827.20102", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20250609", "size_mb": 16.96, "executable_path": "", "uninstall_string": "MsiExec.exe /X{90160000-008C-0000-1000-0000000FF1CE}", "source": "Registry"}, {"name": "CAJAX", "version": "3.0.1", "publisher": "TTKN", "install_location": "", "install_date": "20210731", "size_mb": 16.41, "executable_path": "", "uninstall_string": "MsiExec.exe /I{AAA72CBE-543A-402F-8CC5-68E7FE488AD2}", "source": "Registry"}, {"name": "<PERSON>tto 3.24.214.0", "version": "3.24.214.0", "publisher": "<PERSON>", "install_location": "F:\\Program Files\\Ditto\\", "install_date": "20221230", "size_mb": 15.8, "executable_path": "F:\\Program Files\\Ditto\\Ditto.exe", "uninstall_string": "\"F:\\Program Files\\Ditto\\unins000.exe\"", "source": "Registry"}, {"name": "Python 3.12.1 Tcl/Tk Support (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 15.39, "executable_path": "", "uninstall_string": "MsiExec.exe /I{926CDC62-3AE2-422B-9858-D6EC3BAD473F}", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2019", "version": "16.0.31110", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "", "size_mb": 14.2, "executable_path": "", "uninstall_string": "\"C:\\ProgramData\\Package Cache\\{f3fbabb4-bcfb-45eb-8fff-9b784fd68c38}\\vsta_setup.exe\"  /uninstall", "source": "Registry"}, {"name": "AMD User Experience Program Installer", "version": "1850.03.05.1217", "publisher": "Advanced Micro Devices, Inc.", "install_location": "C:\\Program Files\\AMD\\Performance Profile Client\\", "install_date": "20190518", "size_mb": 12.98, "executable_path": "C:\\Program Files\\AMD\\Performance Profile Client\\AUEPLauncher.exe", "uninstall_string": "", "source": "Registry"}, {"name": "AMD Problem Report Wizard", "version": "3.1.723.0", "publisher": "##COMPANY_NAME##", "install_location": "C:\\Program Files\\AMD\\", "install_date": "20190518", "size_mb": 12.77, "executable_path": "C:\\Program Files\\AMD\\CIM\\BIN64\\AMDCleanupUtility.exe", "uninstall_string": "MsiExec.exe /X{C96D8509-A046-4162-EE3F-5CE2849DE6A7}", "source": "Registry"}, {"name": "File Checker Plugin version 2.12", "version": "2.12", "publisher": "My Company, Inc.", "install_location": "C:\\Program Files (x86)\\FileCheckerPlugin\\", "install_date": "20210123", "size_mb": 11.49, "executable_path": "C:\\Program Files (x86)\\FileCheckerPlugin\\unins000.exe", "uninstall_string": "\"C:\\Program Files (x86)\\FileCheckerPlugin\\unins000.exe\"", "source": "Registry"}, {"name": "Microsoft Visual Studio 2005 Remote Debugger Light (x64) - ENU", "version": "", "publisher": "Microsoft Corporation", "install_location": "C:\\Program Files\\Microsoft Visual Studio 8\\", "install_date": "", "size_mb": 10.76, "executable_path": "C:\\Program Files\\Microsoft Visual Studio 8\\Common7\\IDE\\Remote Debugger\\x64\\mpishim.exe", "uninstall_string": "C:\\Program Files\\Microsoft Visual Studio 8\\Microsoft Visual Studio 2005 Remote Debugger Light (x64) - ENU\\install.exe", "source": "Registry"}, {"name": "AutoCAD 2022 - 简体中文 (Simplified Chinese)", "version": "*********", "publisher": "Autodesk", "install_location": "F:\\Program Files (x86)\\AutoCAD 2022\\", "install_date": "20230621", "size_mb": 10.39, "executable_path": "F:\\Program Files (x86)\\AutoCAD 2022\\acad.exe", "uninstall_string": "", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2015", "version": "14.0.23829", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "", "size_mb": 8.73, "executable_path": "", "uninstall_string": "\"C:\\ProgramData\\Package Cache\\{ab213ab7-4792-4c6f-a3fa-8485d06c3475}\\vsta_setup.exe\"  /uninstall", "source": "Registry"}, {"name": "CA证书控件 1.00", "version": "1.00", "publisher": "kairende", "install_location": "D:\\Program Files (x86)\\kairende\\CA证书控件\\", "install_date": "20210123", "size_mb": 7.58, "executable_path": "", "uninstall_string": "D:\\Program Files (x86)\\kairende\\CA证书控件\\Uninstall.exe", "source": "Registry"}, {"name": "Office 16 Click-to-Run Licensing Component", "version": "16.0.18827.20140", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20250609", "size_mb": 7.06, "executable_path": "", "uninstall_string": "MsiExec.exe /I{90160000-007E-0000-1000-0000000FF1CE}", "source": "Registry"}, {"name": "WinRAR 5.71 (64-位)", "version": "5.71.0", "publisher": "win.rar GmbH", "install_location": "D:\\Program Files\\WinRAR\\", "install_date": "", "size_mb": 6.78, "executable_path": "D:\\Program Files\\WinRAR\\Rar.exe", "uninstall_string": "D:\\Program Files\\WinRAR\\uninstall.exe", "source": "Registry"}, {"name": "Python 3.12.1 Core Interpreter (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 6.73, "executable_path": "", "uninstall_string": "MsiExec.exe /I{AC82C1A3-9597-40F2-893D-F02F778FBA4D}", "source": "Registry"}, {"name": "Autodesk 保存到 Web 和 Mobile", "version": "3.0.29", "publisher": "Autodesk", "install_location": "F:\\Program Files (x86)\\", "install_date": "20230621", "size_mb": 6.68, "executable_path": "F:\\Program Files (x86)\\alipay\\aliedit\\*********\\HotFixTool.exe", "uninstall_string": "MsiExec.exe /X{192B349F-C3F7-4BBE-B49E-00DD4BD28373}", "source": "Registry"}, {"name": "Microsoft Visual Studio 2010 Tools for Office Runtime (x64)", "version": "10.0.60910", "publisher": "Microsoft Corporation", "install_location": "C:\\Program Files\\Common Files\\Microsoft Shared\\VSTO\\10.0\\", "install_date": "", "size_mb": 6.52, "executable_path": "", "uninstall_string": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\VSTO\\10.0\\Microsoft Visual Studio 2010 Tools for Office Runtime (x64)\\install.exe\"", "source": "Registry"}, {"name": "Microsoft Visual Studio 2010 Tools for Office Runtime (x64)语言包 - 简体中文", "version": "10.0.60910", "publisher": "Microsoft Corporation", "install_location": "C:\\Program Files\\Common Files\\Microsoft Shared\\VSTO\\10.0\\", "install_date": "", "size_mb": 6.52, "executable_path": "", "uninstall_string": "\"C:\\Program Files\\Common Files\\Microsoft Shared\\VSTO\\10.0\\Microsoft Visual Studio 2010 Tools for Office Runtime (x64) Language Pack - CHS\\install.exe\"", "source": "Registry"}, {"name": "TP-LINK无线网卡产品", "version": "2.0.0.1", "publisher": "TP-LINK", "install_location": "C:\\Program Files (x86)\\TP-LINK\\TL-WN823N免驱版 2.0\\", "install_date": "20190518", "size_mb": 6.47, "executable_path": "C:\\Program Files (x86)\\TP-LINK\\TL-WN823N免驱版 2.0\\unins000.exe", "uninstall_string": "\"C:\\Program Files (x86)\\TP-LINK\\TL-WN823N免驱版 2.0\\unins000.exe\"", "source": "Registry"}, {"name": "Cisco Secure Client - AnyConnect VPN ", "version": "5.1.7.80", "publisher": "Cisco Systems, Inc.", "install_location": "", "install_date": "", "size_mb": 5.86, "executable_path": "", "uninstall_string": "C:\\Program Files (x86)\\Cisco\\Cisco Secure Client\\Uninstall.exe -remove", "source": "Registry"}, {"name": "PuTTY release 0.83 (64-bit)", "version": "0.83.0.0", "publisher": "<PERSON>", "install_location": "", "install_date": "20250422", "size_mb": 5.8, "executable_path": "", "uninstall_string": "MsiExec.exe /X{ED41CD4E-33BB-400C-AB20-B09388DC83EF}", "source": "Registry"}, {"name": "Microsoft Visual Studio Setup WMI Provider", "version": "3.8.2091.34612", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20231227", "size_mb": 4.45, "executable_path": "", "uninstall_string": "MsiExec.exe /I{9E0059DE-74E7-49A5-8F2A-C17B5BE58B4C}", "source": "Registry"}, {"name": "Epic Games Launcher Prerequisites (x64)", "version": "*******", "publisher": "Epic Games, Inc.", "install_location": "", "install_date": "20221226", "size_mb": 3.48, "executable_path": "", "uninstall_string": "MsiExec.exe /X{F9C5C994-F6B9-4D75-B3E7-AD01B84073E9}", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2015 x86 Hosting Support", "version": "14.0.23829", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20221118", "size_mb": 3.19, "executable_path": "", "uninstall_string": "MsiExec.exe /X{11A9EF3E-6616-31B1-82BC-1080366FA34D}", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2019 x86 Hosting Support", "version": "16.0.31110", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20241224", "size_mb": 3.0, "executable_path": "", "uninstall_string": "MsiExec.exe /X{E7A0CD34-1F9B-3496-ADB3-2F180D302F6A}", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2019 x64 Hosting Support", "version": "16.0.31110", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20241224", "size_mb": 2.46, "executable_path": "", "uninstall_string": "MsiExec.exe /X{8E7A3713-551D-333A-9271-10EF4D77A80F}", "source": "Registry"}, {"name": "Java Auto Updater", "version": "2.8.381.9", "publisher": "Oracle Corporation", "install_location": "", "install_date": "20230722", "size_mb": 2.36, "executable_path": "", "uninstall_string": "", "source": "Registry"}, {"name": "Python 3.12.1 Executables (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 2.24, "executable_path": "", "uninstall_string": "MsiExec.exe /I{44BC9F9C-15C2-46C1-B88D-3135A9DA555F}", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2015 x64 Hosting Support", "version": "14.0.23829", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20221118", "size_mb": 2.23, "executable_path": "", "uninstall_string": "MsiExec.exe /X{A8C30947-7C1B-3A31-8FD8-CEC6D3357D34}", "source": "Registry"}, {"name": "Python 3.12.1 Development Libraries (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 2.11, "executable_path": "", "uninstall_string": "MsiExec.exe /I{8C53CBDD-4DAF-426F-9478-6C7C2920CDDA}", "source": "Registry"}, {"name": "Bonjour", "version": "*******", "publisher": "Apple Inc.", "install_location": "C:\\Program Files (x86)\\Bonjour\\", "install_date": "20220620", "size_mb": 2.02, "executable_path": "C:\\Program Files (x86)\\Bonjour\\mDNSResponder.exe", "uninstall_string": "MsiExec.exe /X{56DDDFB8-7F79-4480-89D5-25E1F52AB28F}", "source": "Registry"}, {"name": "Branding64", "version": "1.00.0001", "publisher": "Advanced Micro Devices, Inc.", "install_location": "C:\\Program Files\\AMD\\", "install_date": "20190518", "size_mb": 1.71, "executable_path": "C:\\Program Files\\AMD\\CIM\\BIN64\\AMDCleanupUtility.exe", "uninstall_string": "MsiExec.exe /I{EE2AFCE4-0238-4DE0-A140-1647021627C1}", "source": "Registry"}, {"name": "Microsoft Visual Studio Setup Configuration", "version": "3.8.2091.34612", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20231227", "size_mb": 1.67, "executable_path": "", "uninstall_string": "MsiExec.exe /I{C777E5A3-D26A-4F0D-84AC-79ECE7560EA5}", "source": "Registry"}, {"name": "Python Launcher", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 1.48, "executable_path": "", "uninstall_string": "MsiExec.exe /X{4C8D4EC3-F620-4CEE-8BAD-B59A3C6815F3}", "source": "Registry"}, {"name": "WinFsp 2023", "version": "2.0.23075", "publisher": "Navimatics LLC", "install_location": "", "install_date": "20240620", "size_mb": 1.43, "executable_path": "", "uninstall_string": "MsiExec.exe /I{E4C768C9-0ED1-4E8D-9B05-CC533F7D1B1A}", "source": "Registry"}, {"name": "Microsoft Visual Studio 2010 Tools for Office Runtime (x64) Language Pack - CHS", "version": "10.0.60910", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20250111", "size_mb": 0.85, "executable_path": "", "uninstall_string": "", "source": "Registry"}, {"name": "SoapToolkit30", "version": "4.00.0000", "publisher": "CAXA", "install_location": "C:\\Program Files (x86)\\", "install_date": "20211124", "size_mb": 0.74, "executable_path": "C:\\Program Files (x86)\\AlibabaProtect\\1.0.70.716\\AlibabaProtect.exe", "uninstall_string": "MsiExec.exe /I{FAF4A2DC-80CD-4BA3-8914-C18E915E5F02}", "source": "Registry"}, {"name": "Mozilla Maintenance Service", "version": "100.0", "publisher": "Mozilla", "install_location": "", "install_date": "", "size_mb": 0.52, "executable_path": "", "uninstall_string": "\"C:\\Program Files (x86)\\Mozilla Maintenance Service\\uninstall.exe\"", "source": "Registry"}, {"name": "Python 3.12.1 pip <PERSON> (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 0.26, "executable_path": "", "uninstall_string": "MsiExec.exe /I{1662F43B-2337-4FD8-8CE6-BEA38FC94DD4}", "source": "Registry"}, {"name": "ACAD Private", "version": "*********", "publisher": "Autodesk", "install_location": "F:\\Program Files (x86)\\AutoCAD 2022\\", "install_date": "20230621", "size_mb": 0.26, "executable_path": "F:\\Program Files (x86)\\AutoCAD 2022\\acad.exe", "uninstall_string": "", "source": "Registry"}, {"name": "Microsoft Visual Studio Tools for Applications 2015 Finalizer", "version": "14.0.23829", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20221118", "size_mb": 0.11, "executable_path": "", "uninstall_string": "MsiExec.exe /I{F93E37BD-4053-37CA-A7BB-A5B74508006C}", "source": "Registry"}, {"name": "Python 3.12.1 Add to Path (64-bit)", "version": "3.12.1150.0", "publisher": "Python Software Foundation", "install_location": "", "install_date": "20231227", "size_mb": 0.05, "executable_path": "", "uninstall_string": "MsiExec.exe /I{946DC818-F8CA-463A-BE16-946EB508BD48}", "source": "Registry"}, {"name": "Office 16 Click-to-Run Localization Component", "version": "16.0.18827.20102", "publisher": "Microsoft Corporation", "install_location": "", "install_date": "20250609", "size_mb": 0.03, "executable_path": "", "uninstall_string": "MsiExec.exe /X{90160000-008C-0804-1000-0000000FF1CE}", "source": "Registry"}, {"name": "Everything 1.4.1.935 (x64)", "version": "1.4.1.935", "publisher": "<PERSON>", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "D:\\Program Files\\Everything\\Uninstall.exe", "source": "Registry"}, {"name": "火绒安全软件", "version": "6.0.6.3", "publisher": "北京火绒网络科技有限公司", "install_location": "", "install_date": "20190518", "size_mb": 0, "executable_path": "", "uninstall_string": "D:\\Program Files (x86)\\Huorong\\Sysdiag\\uninst.exe", "source": "Registry"}, {"name": "Microsoft SQL Server 2014 (64-bit)", "version": "", "publisher": "", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "Registry"}, {"name": "wkhtmltox 0.12.6-1", "version": "", "publisher": "", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "\"F:\\Program Files (x86)\\wkhtmltopdf\\uninstall.exe\"", "source": "Registry"}, {"name": "支付宝安全控件 *********", "version": "*********", "publisher": "Alipay.com Co., Ltd.", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "C:\\Program Files (x86)\\alipay\\aliedit\\*********\\uninst.exe", "source": "Registry"}, {"name": "Cult3D ActiveX Player", "version": "", "publisher": "", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "C:\\WINDOWS\\unvise32.exe C:\\WINDOWS\\system32\\Cult3D\\Uninstall_C3DActiveX.log", "source": "Registry"}, {"name": "e-message", "version": "4.0.24", "publisher": "上海泛微网络股份有限公司", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "F:\\Program Files (x86)\\e-message\\uninstall.exe", "source": "Registry"}, {"name": "Foxmail", "version": "7.2.25.375", "publisher": "腾讯公司", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "F:\\Program Files\\Foxmail 7.2\\uninst.exe", "source": "Registry"}, {"name": "OBS Studio", "version": "30.2.2", "publisher": "OBS Project", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\obs-studio\\uninstall.exe\"", "source": "Registry"}, {"name": "ToDesk", "version": "4.7.6.3", "publisher": "Hainan YouQu Technology Co., Ltd", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "F:\\Program Files\\ToDesk\\uninst.exe", "source": "Registry"}, {"name": "WeGame", "version": "", "publisher": "WeGame", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "F:\\Program Files (x86)\\WeGame\\uninstall_complete.exe", "source": "Registry"}, {"name": "英雄联盟", "version": "", "publisher": "Tencent", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "F:\\Program Files (x86)\\英雄联盟(26)\\英雄联盟卸载.exe", "source": "Registry"}, {"name": "腾讯云 OrcaTerm", "version": "1.0", "publisher": "Google\\Chrome", "install_location": "", "install_date": "20250223", "size_mb": 0, "executable_path": "", "uninstall_string": "\"F:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" --profile-directory=Default --uninstall-app-id=chmjggbiichkjmeoibmlbfeblhlapifd", "source": "Registry"}, {"name": "Create React App Sam<PERSON>", "version": "1.0", "publisher": "Google\\Chrome", "install_location": "", "install_date": "20230512", "size_mb": 0, "executable_path": "", "uninstall_string": "\"D:\\Program Files\\Google\\Chrome\\Application\\chrome.exe\" --profile-directory=Default --uninstall-app-id=iciphficdplhefcicicngdecclfdhiio", "source": "Registry"}, {"name": "支付宝数字证书组件 *******", "version": "*******", "publisher": "Alipay.com Co., Ltd.", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "C:\\Users\\<USER>\\AppData\\Roaming\\alipay\\cf\\uninst.exe", "source": "Registry"}, {"name": "网银支付助手辅助应用", "version": "0.5.0", "publisher": "Mozilla Online Limited", "install_location": "", "install_date": "", "size_mb": 0, "executable_path": "", "uninstall_string": "\"C:\\Users\\<USER>\\AppData\\Local\\MozillaOnline\\COBA\\uninstaller.exe\"", "source": "Registry"}, {"name": "SOLIDWORKS Visualize Boost 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Visualize Boost\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "SOLIDWORKS Inspection 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Inspection\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "SOLIDWORKS Electrical 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Electrical\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "SOLIDWORKS Composer 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Composer\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "SOLIDWORKS PCB 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS PCB\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "SOLIDWORKS Plastics 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Plastics\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}, {"name": "SOLIDWORKS Visualize 2017 SP0", "version": "25.00.5021", "publisher": "Dassault Systemes SolidWorks Corp", "install_location": "D:\\solidworks\\SOLIDWORKS Visualize\\", "install_date": "20201206", "size_mb": 0, "executable_path": "", "uninstall_string": "", "source": "WMI"}]}