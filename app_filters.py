"""
Windows Application Filters
Defines patterns and rules for filtering out Windows built-in applications
"""

# Publishers to exclude (Windows system components)
SYSTEM_PUBLISHERS = {
    'Microsoft Corporation',
    'Microsoft',
    'Microsoft Windows',
    'Microsoft Windows Hardware Compatibility Publisher',
    'Microsoft Windows Publisher',
    'Windows Software Developer',
    'Intel Corporation',
    'Intel',
    'AMD',
    'NVIDIA Corporation',
    'NVIDIA',
    'Realtek',
    'Realtek Semiconductor Corp.',
}

# Application name patterns to exclude (case-insensitive)
SYSTEM_APP_PATTERNS = {
    # Windows built-in apps
    'windows', 'microsoft edge', 'internet explorer', 'windows media player',
    'windows photo viewer', 'windows defender', 'windows security',
    'calculator', 'notepad', 'paint', 'wordpad', 'snipping tool',
    'sticky notes', 'voice recorder', 'camera', 'photos', 'movies & tv',
    'groove music', 'mail and calendar', 'people', 'xbox', 'skype',
    'cortana', 'mixed reality portal', 'your phone', 'feedback hub',
    'get help', 'microsoft tips', 'solitaire', 'candy crush',
    
    # System components and drivers
    'visual c++', 'microsoft visual c++', '.net framework', 'directx',
    'windows sdk', 'windows driver', 'intel graphics', 'nvidia graphics',
    'amd catalyst', 'realtek audio', 'bluetooth', 'wifi driver',
    'chipset', 'audio driver', 'network adapter',
    
    # Windows updates and patches
    'security update', 'update for windows', 'hotfix', 'kb[0-9]',
    'windows update', 'cumulative update',
    
    # System utilities that come with Windows
    'windows powershell', 'command prompt', 'registry editor',
    'device manager', 'disk cleanup', 'system restore',
    'task scheduler', 'event viewer', 'performance monitor',
}

# Specific application names to exclude (exact matches, case-insensitive)
EXACT_EXCLUSIONS = {
    'Microsoft Edge',
    'Microsoft Edge Update',
    'Microsoft Edge WebView2',
    'Windows Security',
    'Windows Defender',
    'Microsoft Defender',
    'Internet Explorer',
    'Windows Media Player',
    'Windows Photo Viewer',
    'Calculator',
    'Notepad',
    'Paint',
    'WordPad',
    'Snipping Tool',
    'Sticky Notes',
    'Voice Recorder',
    'Camera',
    'Photos',
    'Movies & TV',
    'Groove Music',
    'Mail and Calendar',
    'People',
    'Xbox',
    'Xbox Game Bar',
    'Xbox Identity Provider',
    'Skype',
    'Cortana',
    'Mixed Reality Portal',
    'Your Phone',
    'Phone Link',
    'Feedback Hub',
    'Get Help',
    'Microsoft Tips',
    'Microsoft Solitaire Collection',
    'Candy Crush Saga',
    'Candy Crush Friends Saga',
}

# Installation paths that indicate system applications
SYSTEM_PATHS = {
    'c:\\windows\\',
    'c:\\program files\\windows',
    'c:\\program files (x86)\\windows',
    'c:\\program files\\internet explorer',
    'c:\\program files (x86)\\internet explorer',
    'c:\\program files\\windows media player',
    'c:\\program files (x86)\\windows media player',
}

def is_system_application(app_name, publisher, install_location):
    """
    Determine if an application is a Windows system application that should be excluded
    
    Args:
        app_name (str): Name of the application
        publisher (str): Publisher/developer name
        install_location (str): Installation path
    
    Returns:
        bool: True if the application should be excluded, False otherwise
    """
    if not app_name:
        return True
    
    app_name_lower = app_name.lower()
    publisher_lower = publisher.lower() if publisher else ''
    install_location_lower = install_location.lower() if install_location else ''
    
    # Check exact exclusions
    if app_name in EXACT_EXCLUSIONS:
        return True
    
    # Check system publishers
    if publisher and any(pub.lower() in publisher_lower for pub in SYSTEM_PUBLISHERS):
        # Additional check for Microsoft apps that might be third-party
        if 'office' in app_name_lower or 'visual studio' in app_name_lower:
            return False  # Keep Office and Visual Studio
        return True
    
    # Check application name patterns
    if any(pattern in app_name_lower for pattern in SYSTEM_APP_PATTERNS):
        return True
    
    # Check installation paths
    if install_location and any(path in install_location_lower for path in SYSTEM_PATHS):
        return True
    
    # Additional heuristics
    # Exclude apps with no display name or very short names (likely system components)
    if len(app_name.strip()) < 3:
        return True
    
    # Exclude Windows Store apps (they have specific patterns)
    if 'windowsapps' in install_location_lower:
        return True
    
    return False

def should_include_application(app_info):
    """
    Wrapper function to determine if an application should be included in the final list
    
    Args:
        app_info (dict): Dictionary containing application information
    
    Returns:
        bool: True if the application should be included, False otherwise
    """
    return not is_system_application(
        app_info.get('name', ''),
        app_info.get('publisher', ''),
        app_info.get('install_location', '')
    )
