# Windows Application Scanner

A comprehensive Python tool for scanning and inventorying installed applications on Windows systems. This tool helps create a prioritized list of third-party applications for easy reference when setting up a new computer.

## Features

- **Comprehensive Scanning**: Uses multiple methods (Windows Registry and WMI) to detect installed applications
- **Smart Filtering**: Automatically excludes Windows built-in applications and system components
- **Detailed Information**: Collects application name, version, publisher, size, installation path, and executable path
- **Size-Based Sorting**: Applications are sorted by installation size (largest first) to prioritize important software
- **Multiple Export Formats**: Exports results to CSV, JSON, and human-readable text formats
- **Easy to Use**: Simple batch file execution for non-technical users

## What Gets Scanned

The scanner identifies:
- ✅ Third-party applications (browsers, office suites, development tools, games, etc.)
- ✅ User-installed software from various sources
- ✅ Applications installed via installers, MSI packages, etc.

The scanner excludes:
- ❌ Windows built-in applications (Calculator, Paint, etc.)
- ❌ System components and drivers
- ❌ Windows updates and patches
- ❌ Microsoft Visual C++ redistributables
- ❌ DirectX and .NET Framework components

## Requirements

- Windows 10 or Windows 11
- Python 3.6 or higher
- Administrator privileges (recommended for complete scanning)

## Installation

1. **Download or clone this repository**
2. **Install Python** (if not already installed):
   - Download from [python.org](https://python.org)
   - During installation, make sure to check "Add Python to PATH"

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```
   Or manually install:
   ```bash
   pip install pywin32 wmi
   ```

## Usage

### Method 1: Easy Execution (Recommended)
1. Double-click `run_scanner.bat`
2. The batch file will automatically install dependencies and run the scanner
3. Wait for the scan to complete (may take several minutes)
4. Check the generated files in the same directory

### Method 2: Command Line
```bash
python windows_app_scanner.py
```

### Method 3: Run as Administrator (for complete access)
1. Right-click on Command Prompt and select "Run as administrator"
2. Navigate to the scanner directory
3. Run: `python windows_app_scanner.py`

## Output Files

The scanner generates three files with timestamps:

1. **`installed_apps_YYYYMMDD_HHMMSS.csv`** - Spreadsheet format for analysis
2. **`installed_apps_YYYYMMDD_HHMMSS.json`** - Machine-readable format with metadata
3. **`installed_apps_YYYYMMDD_HHMMSS.txt`** - Human-readable format with detailed information

## Sample Output

```
WINDOWS INSTALLED APPLICATIONS INVENTORY
==================================================

Scan Date: 2024-01-15 14:30:25
Total Applications Found: 45

Applications sorted by size (largest first):
--------------------------------------------------

  1. Adobe Photoshop 2024
     Version: 25.0.0
     Publisher: Adobe Inc.
     Size: 2847.3 MB
     Install Path: C:\Program Files\Adobe\Adobe Photoshop 2024
     Executable: C:\Program Files\Adobe\Adobe Photoshop 2024\Photoshop.exe
     Source: Registry

  2. Microsoft Visual Studio Code
     Version: 1.85.1
     Publisher: Microsoft Corporation
     Size: 312.7 MB
     Install Path: C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code
     Source: Registry
```

## Understanding the Results

### Size Information
- **Accurate sizes**: From Windows Registry (most reliable)
- **Calculated sizes**: Directory scanning when registry data unavailable
- **Unknown sizes**: When size cannot be determined

### Source Information
- **Registry**: Found in Windows Registry uninstall entries
- **WMI**: Detected via Windows Management Instrumentation

## Troubleshooting

### Common Issues

**"Permission denied" errors**:
- Run Command Prompt as Administrator
- Some registry keys require elevated privileges

**"WMI module not available"**:
- Install with: `pip install wmi`
- Scanner will work without WMI but may find fewer applications

**Python not found**:
- Install Python from python.org
- Make sure "Add Python to PATH" was checked during installation
- Restart Command Prompt after Python installation

**Slow scanning**:
- Normal behavior - scanning can take 5-15 minutes
- Large installations or many applications increase scan time
- Size calculation for directories without registry data is time-consuming

### Performance Tips

- Run as Administrator for faster registry access
- Close other applications during scanning
- For faster results, comment out `enhance_size_information()` call in the code

## Customization

### Modifying Filters
Edit `app_filters.py` to customize which applications are excluded:
- Add publishers to `SYSTEM_PUBLISHERS`
- Add name patterns to `SYSTEM_APP_PATTERNS`
- Add exact names to `EXACT_EXCLUSIONS`

### Adding Export Formats
Extend the `WindowsAppScanner` class with additional export methods.

## Use Cases

1. **New Computer Setup**: Prioritized list of applications to reinstall
2. **System Migration**: Complete inventory before moving to new hardware
3. **Software Audit**: Understanding what's installed on corporate machines
4. **Cleanup Planning**: Identifying large applications for removal
5. **Backup Planning**: Knowing which applications need to be backed up

## Technical Details

### Scanning Methods
1. **Registry Scanning**: Primary method using Windows uninstall registry keys
   - `HKEY_LOCAL_MACHINE\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`
   - `HKEY_LOCAL_MACHINE\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall`
   - `HKEY_CURRENT_USER\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall`

2. **WMI Scanning**: Secondary method using Win32_Product class
   - Slower but can find additional applications
   - Requires WMI module installation

### Size Calculation
- Registry `EstimatedSize` values (most accurate)
- Directory scanning for missing size data
- Conversion from KB to MB for readability

## License

This project is provided as-is for educational and personal use. Feel free to modify and distribute according to your needs.

## Contributing

Contributions are welcome! Please feel free to submit issues, feature requests, or pull requests.

## Support

If you encounter issues:
1. Check the troubleshooting section above
2. Ensure you're running with appropriate permissions
3. Verify Python and dependencies are properly installed
4. Check the console output for specific error messages
