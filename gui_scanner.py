#!/usr/bin/env python3
"""
Windows Application Scanner - GUI Version
Simple graphical interface for the application scanner
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
from datetime import datetime
from windows_app_scanner import WindowsAppScanner

class AppScannerGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Windows Application Scanner")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        self.scanner = None
        self.applications = []
        self.scanning = False
        
        self.setup_ui()
    
    def setup_ui(self):
        """Set up the user interface"""
        # Main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text="Windows Application Scanner", 
                               font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Control frame
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        control_frame.columnconfigure(1, weight=1)
        
        # Scan button
        self.scan_button = ttk.Button(control_frame, text="Start Scan", 
                                     command=self.start_scan)
        self.scan_button.grid(row=0, column=0, padx=(0, 10))
        
        # Progress bar
        self.progress = ttk.Progressbar(control_frame, mode='indeterminate')
        self.progress.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # Export button
        self.export_button = ttk.Button(control_frame, text="Export Results", 
                                       command=self.export_results, state='disabled')
        self.export_button.grid(row=0, column=2)
        
        # Results frame
        results_frame = ttk.LabelFrame(main_frame, text="Scan Results", padding="5")
        results_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Treeview for results
        columns = ('Name', 'Version', 'Publisher', 'Size (MB)')
        self.tree = ttk.Treeview(results_frame, columns=columns, show='headings', height=15)
        
        # Configure columns
        self.tree.heading('Name', text='Application Name')
        self.tree.heading('Version', text='Version')
        self.tree.heading('Publisher', text='Publisher')
        self.tree.heading('Size (MB)', text='Size (MB)')
        
        self.tree.column('Name', width=300)
        self.tree.column('Version', width=100)
        self.tree.column('Publisher', width=200)
        self.tree.column('Size (MB)', width=100)
        
        # Scrollbars
        v_scrollbar = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.tree.yview)
        h_scrollbar = ttk.Scrollbar(results_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Grid treeview and scrollbars
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        v_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        h_scrollbar.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # Status frame
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        status_frame.columnconfigure(0, weight=1)
        
        # Status label
        self.status_label = ttk.Label(status_frame, text="Ready to scan")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # App count label
        self.count_label = ttk.Label(status_frame, text="")
        self.count_label.grid(row=0, column=1, sticky=tk.E)
    
    def start_scan(self):
        """Start the application scan in a separate thread"""
        if self.scanning:
            return
        
        self.scanning = True
        self.scan_button.config(state='disabled', text='Scanning...')
        self.export_button.config(state='disabled')
        self.progress.start()
        self.status_label.config(text="Scanning installed applications...")
        
        # Clear previous results
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # Start scan in separate thread
        scan_thread = threading.Thread(target=self.perform_scan)
        scan_thread.daemon = True
        scan_thread.start()
    
    def perform_scan(self):
        """Perform the actual scan (runs in separate thread)"""
        try:
            self.scanner = WindowsAppScanner()
            self.applications = self.scanner.scan_all_applications()
            
            # Update UI in main thread
            self.root.after(0, self.scan_complete)
            
        except Exception as e:
            # Handle errors in main thread
            self.root.after(0, lambda: self.scan_error(str(e)))
    
    def scan_complete(self):
        """Handle scan completion (runs in main thread)"""
        self.scanning = False
        self.progress.stop()
        self.scan_button.config(state='normal', text='Start Scan')
        self.export_button.config(state='normal')
        
        # Populate results
        for app in self.applications:
            self.tree.insert('', tk.END, values=(
                app['name'],
                app['version'],
                app['publisher'],
                f"{app['size_mb']:.1f}" if app['size_mb'] > 0 else "Unknown"
            ))
        
        # Update status
        app_count = len(self.applications)
        total_size = sum(app['size_mb'] for app in self.applications)
        
        self.status_label.config(text=f"Scan complete! Found {app_count} applications")
        self.count_label.config(text=f"Total size: {total_size:.1f} MB ({total_size/1024:.1f} GB)")
        
        if app_count == 0:
            messagebox.showinfo("Scan Complete", "No third-party applications found.")
        else:
            messagebox.showinfo("Scan Complete", 
                              f"Found {app_count} third-party applications.\n"
                              f"Total estimated size: {total_size:.1f} MB")
    
    def scan_error(self, error_message):
        """Handle scan errors (runs in main thread)"""
        self.scanning = False
        self.progress.stop()
        self.scan_button.config(state='normal', text='Start Scan')
        self.status_label.config(text="Scan failed")
        
        messagebox.showerror("Scan Error", 
                           f"An error occurred during scanning:\n\n{error_message}\n\n"
                           f"Try running as Administrator for better results.")
    
    def export_results(self):
        """Export scan results to files"""
        if not self.applications:
            messagebox.showwarning("No Data", "No scan results to export. Please run a scan first.")
            return
        
        # Ask user to select export directory
        export_dir = filedialog.askdirectory(title="Select Export Directory")
        if not export_dir:
            return
        
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            csv_file = os.path.join(export_dir, f"installed_apps_{timestamp}.csv")
            json_file = os.path.join(export_dir, f"installed_apps_{timestamp}.json")
            txt_file = os.path.join(export_dir, f"installed_apps_{timestamp}.txt")
            
            self.scanner.export_to_csv(csv_file)
            self.scanner.export_to_json(json_file)
            self.scanner.export_to_text(txt_file)
            
            messagebox.showinfo("Export Complete", 
                              f"Results exported successfully!\n\n"
                              f"Files created:\n"
                              f"• {os.path.basename(csv_file)}\n"
                              f"• {os.path.basename(json_file)}\n"
                              f"• {os.path.basename(txt_file)}\n\n"
                              f"Location: {export_dir}")
            
        except Exception as e:
            messagebox.showerror("Export Error", f"Failed to export results:\n\n{str(e)}")
    
    def on_closing(self):
        """Handle application closing"""
        if self.scanning:
            if messagebox.askokcancel("Quit", "Scan is in progress. Do you want to quit?"):
                self.root.destroy()
        else:
            self.root.destroy()

def main():
    """Main function to run the GUI application"""
    root = tk.Tk()
    app = AppScannerGUI(root)
    
    # Handle window closing
    root.protocol("WM_DELETE_WINDOW", app.on_closing)
    
    # Center window on screen
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
