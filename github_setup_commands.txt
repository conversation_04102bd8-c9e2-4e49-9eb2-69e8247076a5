GitHub仓库设置命令
==================

创建GitHub仓库后，请在命令提示符中运行以下命令：

1. 添加远程仓库（请替换YOUR_USERNAME为您的GitHub用户名）：
git remote add origin https://github.com/YOUR_USERNAME/windows-application-scanner.git

2. 设置主分支名称：
git branch -M main

3. 推送代码到GitHub：
git push -u origin main

完整示例（请替换YOUR_USERNAME）：
git remote add origin https://github.com/YOUR_USERNAME/windows-application-scanner.git
git branch -M main
git push -u origin main

注意：
- 请将YOUR_USERNAME替换为您的实际GitHub用户名
- 如果您的仓库名称不同，请相应修改URL
- 首次推送时可能需要输入GitHub用户名和密码（或Personal Access Token）
