# Windows Application Scanner - PowerShell Version
# Alternative implementation for users who prefer PowerShell

param(
    [string]$OutputPath = ".",
    [switch]$IncludeSystemApps = $false,
    [switch]$Verbose = $false
)

# Set up logging
if ($Verbose) {
    $VerbosePreference = "Continue"
}

Write-Host "Windows Application Scanner (PowerShell Version)" -ForegroundColor Green
Write-Host "=" * 50 -ForegroundColor Green

# Define system publishers and apps to exclude
$SystemPublishers = @(
    "Microsoft Corporation",
    "Microsoft",
    "Intel Corporation", 
    "Intel",
    "AMD",
    "NVIDIA Corporation",
    "NVIDIA",
    "Realtek"
)

$SystemAppPatterns = @(
    "Windows",
    "Microsoft Edge",
    "Internet Explorer", 
    "Windows Media Player",
    "Calculator",
    "Notepad",
    "Paint",
    "WordPad",
    "Visual C++",
    ".NET Framework",
    "DirectX",
    "Windows SDK",
    "Security Update",
    "Update for Windows",
    "Hotfix"
)

function Test-SystemApplication {
    param(
        [string]$Name,
        [string]$Publisher
    )
    
    if (-not $IncludeSystemApps) {
        # Check if publisher is a system publisher
        foreach ($sysPub in $SystemPublishers) {
            if ($Publisher -like "*$sysPub*") {
                # Exception for Office and Visual Studio
                if ($Name -like "*Office*" -or $Name -like "*Visual Studio*") {
                    return $false
                }
                return $true
            }
        }
        
        # Check if name matches system app patterns
        foreach ($pattern in $SystemAppPatterns) {
            if ($Name -like "*$pattern*") {
                return $true
            }
        }
    }
    
    return $false
}

function Get-InstalledApplications {
    Write-Host "Scanning installed applications..." -ForegroundColor Yellow
    
    $applications = @()
    
    # Registry paths to scan
    $registryPaths = @(
        "HKLM:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*",
        "HKLM:\SOFTWARE\WOW6432Node\Microsoft\Windows\CurrentVersion\Uninstall\*",
        "HKCU:\SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*"
    )
    
    foreach ($path in $registryPaths) {
        Write-Verbose "Scanning registry path: $path"
        
        try {
            $items = Get-ItemProperty $path -ErrorAction SilentlyContinue
            
            foreach ($item in $items) {
                if ($item.DisplayName) {
                    $app = [PSCustomObject]@{
                        Name = $item.DisplayName
                        Version = $item.DisplayVersion
                        Publisher = $item.Publisher
                        InstallDate = $item.InstallDate
                        InstallLocation = $item.InstallLocation
                        UninstallString = $item.UninstallString
                        SizeKB = $item.EstimatedSize
                        SizeMB = if ($item.EstimatedSize) { [math]::Round($item.EstimatedSize / 1024, 2) } else { 0 }
                        Source = "Registry"
                    }
                    
                    # Filter out system applications
                    if (-not (Test-SystemApplication -Name $app.Name -Publisher $app.Publisher)) {
                        $applications += $app
                    }
                }
            }
        }
        catch {
            Write-Verbose "Could not access registry path: $path"
        }
    }
    
    # Remove duplicates based on name and publisher
    $uniqueApps = $applications | Sort-Object Name, Publisher | Get-Unique -AsString
    
    Write-Host "Found $($uniqueApps.Count) third-party applications" -ForegroundColor Green
    return $uniqueApps
}

function Get-DirectorySize {
    param([string]$Path)
    
    if (-not (Test-Path $Path)) {
        return 0
    }
    
    try {
        $size = (Get-ChildItem $Path -Recurse -File -ErrorAction SilentlyContinue | 
                Measure-Object -Property Length -Sum).Sum
        return [math]::Round($size / 1MB, 2)
    }
    catch {
        return 0
    }
}

function Export-ToCSV {
    param(
        [array]$Applications,
        [string]$FilePath
    )
    
    Write-Host "Exporting to CSV: $FilePath" -ForegroundColor Yellow
    
    $Applications | Select-Object Name, Version, Publisher, SizeMB, InstallLocation, InstallDate, Source |
        Export-Csv -Path $FilePath -NoTypeInformation -Encoding UTF8
    
    Write-Host "CSV export complete" -ForegroundColor Green
}

function Export-ToJSON {
    param(
        [array]$Applications,
        [string]$FilePath
    )
    
    Write-Host "Exporting to JSON: $FilePath" -ForegroundColor Yellow
    
    $exportData = @{
        ScanDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
        TotalApplications = $Applications.Count
        Applications = $Applications
    }
    
    $exportData | ConvertTo-Json -Depth 3 | Out-File -FilePath $FilePath -Encoding UTF8
    
    Write-Host "JSON export complete" -ForegroundColor Green
}

function Export-ToText {
    param(
        [array]$Applications,
        [string]$FilePath
    )
    
    Write-Host "Exporting to text: $FilePath" -ForegroundColor Yellow
    
    $content = @()
    $content += "WINDOWS INSTALLED APPLICATIONS INVENTORY"
    $content += "=" * 50
    $content += ""
    $content += "Scan Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"
    $content += "Total Applications Found: $($Applications.Count)"
    $content += ""
    $content += "Applications sorted by size (largest first):"
    $content += "-" * 50
    $content += ""
    
    for ($i = 0; $i -lt $Applications.Count; $i++) {
        $app = $Applications[$i]
        $content += "$($i + 1). $($app.Name)"
        $content += "     Version: $($app.Version)"
        $content += "     Publisher: $($app.Publisher)"
        $content += "     Size: $($app.SizeMB) MB"
        if ($app.InstallLocation) {
            $content += "     Install Path: $($app.InstallLocation)"
        }
        if ($app.InstallDate) {
            $content += "     Install Date: $($app.InstallDate)"
        }
        $content += "     Source: $($app.Source)"
        $content += ""
    }
    
    $content | Out-File -FilePath $FilePath -Encoding UTF8
    
    Write-Host "Text export complete" -ForegroundColor Green
}

function Show-Summary {
    param([array]$Applications)
    
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host "SCAN SUMMARY" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    Write-Host "Total third-party applications found: $($Applications.Count)" -ForegroundColor White
    
    $totalSize = ($Applications | Measure-Object -Property SizeMB -Sum).Sum
    Write-Host "Total estimated size: $([math]::Round($totalSize, 2)) MB ($([math]::Round($totalSize/1024, 2)) GB)" -ForegroundColor White
    
    Write-Host ""
    Write-Host "Top 10 largest applications:" -ForegroundColor Yellow
    Write-Host "-" * 60 -ForegroundColor Yellow
    
    $top10 = $Applications | Sort-Object SizeMB -Descending | Select-Object -First 10
    for ($i = 0; $i -lt $top10.Count; $i++) {
        $app = $top10[$i]
        $sizeStr = if ($app.SizeMB -gt 0) { "$($app.SizeMB) MB" } else { "Unknown size" }
        Write-Host ("{0,2}. {1,-40} {2,15}" -f ($i + 1), $app.Name.Substring(0, [math]::Min(40, $app.Name.Length)), $sizeStr)
    }
}

# Main execution
try {
    $applications = Get-InstalledApplications
    
    if ($applications.Count -eq 0) {
        Write-Host "No third-party applications found." -ForegroundColor Red
        exit
    }
    
    # Enhance size information for apps without size data
    Write-Host "Enhancing size information..." -ForegroundColor Yellow
    foreach ($app in $applications) {
        if ($app.SizeMB -eq 0 -and $app.InstallLocation) {
            Write-Verbose "Calculating size for $($app.Name)..."
            $calculatedSize = Get-DirectorySize -Path $app.InstallLocation
            if ($calculatedSize -gt 0) {
                $app.SizeMB = $calculatedSize
            }
        }
    }
    
    # Sort by size (largest first)
    $applications = $applications | Sort-Object SizeMB -Descending
    
    # Show summary
    Show-Summary -Applications $applications
    
    # Export results
    Write-Host ""
    Write-Host "=" * 60 -ForegroundColor Cyan
    Write-Host "EXPORTING RESULTS" -ForegroundColor Cyan
    Write-Host "=" * 60 -ForegroundColor Cyan
    
    $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
    $csvFile = Join-Path $OutputPath "installed_apps_$timestamp.csv"
    $jsonFile = Join-Path $OutputPath "installed_apps_$timestamp.json"
    $txtFile = Join-Path $OutputPath "installed_apps_$timestamp.txt"
    
    Export-ToCSV -Applications $applications -FilePath $csvFile
    Export-ToJSON -Applications $applications -FilePath $jsonFile
    Export-ToText -Applications $applications -FilePath $txtFile
    
    Write-Host ""
    Write-Host "Export complete! Files created:" -ForegroundColor Green
    Write-Host "  - $csvFile (CSV format)" -ForegroundColor White
    Write-Host "  - $jsonFile (JSON format)" -ForegroundColor White
    Write-Host "  - $txtFile (Human-readable text)" -ForegroundColor White
    Write-Host ""
    Write-Host "These files contain a prioritized list of applications" -ForegroundColor Cyan
    Write-Host "sorted by size to help with new computer setup." -ForegroundColor Cyan
}
catch {
    Write-Error "An error occurred: $($_.Exception.Message)"
    Write-Host "Please run PowerShell as Administrator if you encounter permission issues." -ForegroundColor Yellow
}
