@echo off
echo Windows Application Scanner
echo ========================
echo.

REM Change to the directory where this batch file is located
cd /d "%~dp0"
echo Current directory: %CD%
echo.

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python from https://python.org
    echo Make sure to check "Add Python to PATH" during installation
    pause
    exit /b 1
)

echo Python found. Checking dependencies...

REM Install required packages if not already installed
echo Installing required Python packages...
pip install pywin32 wmi

if errorlevel 1 (
    echo.
    echo WARNING: Some packages may not have installed correctly.
    echo The scanner will still work but may have limited functionality.
    echo.
)

REM Check if the scanner file exists
if not exist "windows_app_scanner.py" (
    echo ERROR: windows_app_scanner.py not found in current directory
    echo Please make sure all files are in the same folder
    echo Current directory: %CD%
    pause
    exit /b 1
)

echo.
echo Starting application scan...
echo This may take several minutes depending on the number of installed applications.
echo.

REM Run the scanner
python windows_app_scanner.py

echo.
echo Scan complete! Check the generated files in this directory.
echo.
pause
